"""
Schedules router for the FastAPI application.

This module provides API endpoints for schedule optimization.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
import json
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from pydantic import BaseModel, ConfigDict

from db_config import get_db
from models import Surgery, SurgeryRoomAssignment, OperatingRoom, Surgeon, User, SurgeryType, Patient, ScheduleHistory
from api.models import (
    ScheduleAssignment,
    OptimizationResultEnriched,
    SurgeryEnriched,
    ErrorResponse,
    ScheduleConflict,
    ScheduleValidationResult,
    ManualScheduleAdjustment,
    ScheduleComparison,
    ScheduleHistoryEntry
)
from api.auth import get_current_active_user
from tabu_optimizer import TabuOptimizer
from solution_evaluator import SolutionEvaluator

router = APIRouter()


class OptimizationParameters(BaseModel):
    """Model for optimization parameters."""
    schedule_date: Optional[date] = None
    max_iterations: int = 100
    tabu_tenure: int = 10
    max_no_improvement: int = 20
    time_limit_seconds: int = 300
    weights: Optional[Dict[str, float]] = None


class SurgeryAssignment(BaseModel):
    """Model for surgery assignment response."""
    surgery_id: int
    room_id: int
    start_time: datetime
    end_time: datetime
    surgeon_id: Optional[int] = None
    patient_id: Optional[int] = None
    duration_minutes: int
    surgery_type_id: int

    model_config = ConfigDict(from_attributes=True)


class OptimizationResult(BaseModel):
    """Model for optimization result response."""
    assignments: List[SurgeryAssignment]
    score: float
    metrics: Dict[str, float]
    iteration_count: int
    execution_time_seconds: float


@router.post("/optimize", response_model=OptimizationResult)
async def optimize_schedule(
    params: OptimizationParameters,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Optimize surgery schedule.

    Args:
        params: Optimization parameters
        db: Database session
        current_user: Current authenticated user

    Returns:
        OptimizationResult: Optimized schedule with metrics

    Raises:
        HTTPException: If optimization fails
    """
    try:
        # Get surgeries to schedule
        query = db.query(Surgery)
        if params.date:
            query = query.filter(Surgery.scheduled_date == params.date)
        else:
            # Default to surgeries with status "Scheduled"
            query = query.filter(Surgery.status == "Scheduled")

        surgeries = query.all()
        if not surgeries:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No surgeries found to schedule"
            )

        # Get operating rooms
        operating_rooms = db.query(OperatingRoom).all()
        if not operating_rooms:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No operating rooms found"
            )

        # Create optimizer
        optimizer = TabuOptimizer(
            db_session=db,
            surgeries=surgeries,
            operating_rooms=operating_rooms,
            tabu_tenure=params.tabu_tenure,
            max_iterations=params.max_iterations,
            max_no_improvement=params.max_no_improvement,
            time_limit_seconds=params.time_limit_seconds,
            weights=params.weights
        )

        # Run optimization
        solution = optimizer.optimize()

        # Evaluate solution
        evaluator = SolutionEvaluator(db_session=db)
        score, metrics = evaluator.evaluate(solution)

        # Create response
        assignments = []
        for assignment in solution:
            surgery = db.query(Surgery).filter(Surgery.surgery_id == assignment.surgery_id).first()
            assignments.append(SurgeryAssignment(
                surgery_id=assignment.surgery_id,
                room_id=assignment.room_id,
                start_time=assignment.start_time,
                end_time=assignment.end_time,
                surgeon_id=surgery.surgeon_id if surgery else None,
                patient_id=surgery.patient_id if surgery else None,
                duration_minutes=surgery.duration_minutes if surgery else 0,
                surgery_type_id=surgery.surgery_type_id if surgery else 0
            ))

        return OptimizationResult(
            assignments=assignments,
            score=score,
            metrics=metrics,
            iteration_count=optimizer.iteration_count,
            execution_time_seconds=optimizer.execution_time
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Optimization failed: {str(e)}"
        )


@router.post("/apply", status_code=status.HTTP_200_OK)
async def apply_schedule(
    assignments: List[SurgeryAssignment],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Apply a schedule by creating surgery room assignments.

    Args:
        assignments: List of surgery assignments
        db: Database session
        current_user: Current authenticated user

    Returns:
        Dict: Success message

    Raises:
        HTTPException: If applying schedule fails
    """
    try:
        # Delete existing assignments for these surgeries
        surgery_ids = [a.surgery_id for a in assignments]
        db.query(SurgeryRoomAssignment).filter(
            SurgeryRoomAssignment.surgery_id.in_(surgery_ids)
        ).delete(synchronize_session=False)

        # Create new assignments
        for assignment in assignments:
            db_assignment = SurgeryRoomAssignment(
                surgery_id=assignment.surgery_id,
                room_id=assignment.room_id,
                start_time=assignment.start_time,
                end_time=assignment.end_time
            )
            db.add(db_assignment)

            # Update surgery with room and times
            surgery = db.query(Surgery).filter(Surgery.surgery_id == assignment.surgery_id).first()
            if surgery:
                surgery.room_id = assignment.room_id
                surgery.start_time = assignment.start_time
                surgery.end_time = assignment.end_time

        db.commit()
        return {"message": f"Successfully applied schedule for {len(assignments)} surgeries"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply schedule: {str(e)}"
        )


@router.get("/current", response_model=List[ScheduleAssignment])
async def get_current_schedule(
    date: Optional[date] = Query(None, description="Filter by date"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current schedule with enriched data.

    Args:
        date: Filter by date
        db: Database session
        current_user: Current authenticated user

    Returns:
        List[ScheduleAssignment]: Current schedule with enriched data
    """
    # Query with joins to get all related data
    query = (
        db.query(Surgery, OperatingRoom, Surgeon, Patient, SurgeryType)
        .filter(Surgery.room_id.isnot(None))
        .join(OperatingRoom, Surgery.room_id == OperatingRoom.room_id)
        .outerjoin(Surgeon, Surgery.surgeon_id == Surgeon.surgeon_id)
        .outerjoin(Patient, Surgery.patient_id == Patient.patient_id)
        .outerjoin(SurgeryType, Surgery.surgery_type_id == SurgeryType.type_id)
    )

    if date:
        query = query.filter(Surgery.scheduled_date == date)

    results = query.all()

    assignments = []
    for surgery, room, surgeon, patient, surgery_type in results:
        assignments.append(ScheduleAssignment(
            surgery_id=surgery.surgery_id,
            room_id=surgery.room_id,
            room=f"OR-{room.room_id}" if room else f"OR-{surgery.room_id}",
            surgeon_id=surgery.surgeon_id,
            surgeon=surgeon.name if surgeon else None,
            surgery_type_id=surgery.surgery_type_id,
            surgery_type=surgery_type.name if surgery_type else f"Type {surgery.surgery_type_id}",
            start_time=surgery.start_time,
            end_time=surgery.end_time,
            duration_minutes=surgery.duration_minutes,
            patient_id=surgery.patient_id,
            patient_name=patient.name if patient else None,
            urgency_level=surgery.urgency_level,
            status=surgery.status
        ))

    return assignments


# Enhanced Schedule Management Endpoints

def _detect_schedule_conflicts(db: Session, schedule_date: date) -> List[ScheduleConflict]:
    """
    Detect conflicts in the schedule for a given date.

    Args:
        db: Database session
        schedule_date: Date to check for conflicts

    Returns:
        List of detected conflicts
    """
    conflicts = []

    # Get all scheduled surgeries for the date
    surgeries = (
        db.query(Surgery, OperatingRoom, Surgeon)
        .filter(Surgery.scheduled_date == schedule_date)
        .filter(Surgery.room_id.isnot(None))
        .filter(Surgery.start_time.isnot(None))
        .filter(Surgery.end_time.isnot(None))
        .join(OperatingRoom, Surgery.room_id == OperatingRoom.room_id)
        .outerjoin(Surgeon, Surgery.surgeon_id == Surgeon.surgeon_id)
        .all()
    )

    # Check for time overlaps in the same room
    for i, (surgery1, room1, surgeon1) in enumerate(surgeries):
        for j, (surgery2, room2, surgeon2) in enumerate(surgeries[i+1:], i+1):
            # Room conflicts
            if surgery1.room_id == surgery2.room_id:
                if (surgery1.start_time < surgery2.end_time and
                    surgery2.start_time < surgery1.end_time):
                    conflicts.append(ScheduleConflict(
                        conflict_type="room_overlap",
                        surgery_id=surgery1.surgery_id,
                        conflicting_surgery_id=surgery2.surgery_id,
                        resource_type="room",
                        resource_id=surgery1.room_id,
                        conflict_start=max(surgery1.start_time, surgery2.start_time),
                        conflict_end=min(surgery1.end_time, surgery2.end_time),
                        severity="critical",
                        message=f"Room {room1.location} double-booked between surgeries {surgery1.surgery_id} and {surgery2.surgery_id}"
                    ))

            # Surgeon conflicts
            if (surgery1.surgeon_id and surgery2.surgeon_id and
                surgery1.surgeon_id == surgery2.surgeon_id):
                if (surgery1.start_time < surgery2.end_time and
                    surgery2.start_time < surgery1.end_time):
                    conflicts.append(ScheduleConflict(
                        conflict_type="surgeon_overlap",
                        surgery_id=surgery1.surgery_id,
                        conflicting_surgery_id=surgery2.surgery_id,
                        resource_type="surgeon",
                        resource_id=surgery1.surgeon_id,
                        conflict_start=max(surgery1.start_time, surgery2.start_time),
                        conflict_end=min(surgery1.end_time, surgery2.end_time),
                        severity="critical",
                        message=f"Surgeon {surgeon1.name if surgeon1 else 'Unknown'} double-booked between surgeries {surgery1.surgery_id} and {surgery2.surgery_id}"
                    ))

    return conflicts


@router.get("/conflicts", response_model=ScheduleValidationResult)
async def detect_schedule_conflicts(
    schedule_date: date = Query(..., description="Date to check for conflicts"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Detect conflicts in the schedule for a specific date.

    Args:
        schedule_date: Date to check for conflicts
        db: Database session
        current_user: Current authenticated user

    Returns:
        ScheduleValidationResult: Validation results with conflicts
    """
    conflicts = _detect_schedule_conflicts(db, schedule_date)

    warnings = []
    # Add warnings for surgeries without assigned surgeons
    unassigned_surgeries = (
        db.query(Surgery)
        .filter(Surgery.scheduled_date == schedule_date)
        .filter(Surgery.surgeon_id.is_(None))
        .filter(Surgery.room_id.isnot(None))
        .count()
    )

    if unassigned_surgeries > 0:
        warnings.append(f"{unassigned_surgeries} surgeries do not have assigned surgeons")

    critical_conflicts = len([c for c in conflicts if c.severity == "critical"])

    return ScheduleValidationResult(
        is_valid=len(conflicts) == 0,
        conflicts=conflicts,
        warnings=warnings,
        total_conflicts=len(conflicts),
        critical_conflicts=critical_conflicts
    )


def _save_schedule_history(
    db: Session,
    schedule_date: date,
    user_id: int,
    action_type: str,
    changes_summary: str,
    affected_surgeries: List[int],
    schedule_snapshot: List[ScheduleAssignment]
):
    """Save schedule change to history."""
    history_entry = ScheduleHistory(
        schedule_date=schedule_date,
        created_by_user_id=user_id,
        action_type=action_type,
        changes_summary=changes_summary,
        affected_surgeries=json.dumps(affected_surgeries),
        schedule_snapshot=json.dumps([s.model_dump() for s in schedule_snapshot])
    )
    db.add(history_entry)
    db.commit()
    return history_entry


@router.post("/adjust", response_model=Dict[str, Any])
async def manual_schedule_adjustment(
    adjustment: ManualScheduleAdjustment,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Manually adjust a surgery schedule.

    Args:
        adjustment: Manual adjustment parameters
        db: Database session
        current_user: Current authenticated user

    Returns:
        Dict: Adjustment result with validation information
    """
    # Get the surgery to adjust
    surgery = db.query(Surgery).filter(Surgery.surgery_id == adjustment.surgery_id).first()
    if not surgery:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Surgery with ID {adjustment.surgery_id} not found"
        )

    # Store original values for history
    original_room_id = surgery.room_id
    original_surgeon_id = surgery.surgeon_id
    original_start_time = surgery.start_time
    original_duration = surgery.duration_minutes

    # Prepare changes
    changes = []
    if adjustment.new_room_id and adjustment.new_room_id != original_room_id:
        # Validate room exists
        room = db.query(OperatingRoom).filter(OperatingRoom.room_id == adjustment.new_room_id).first()
        if not room:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Operating room with ID {adjustment.new_room_id} not found"
            )
        changes.append(f"Room changed from {original_room_id} to {adjustment.new_room_id}")

    if adjustment.new_surgeon_id and adjustment.new_surgeon_id != original_surgeon_id:
        # Validate surgeon exists
        surgeon = db.query(Surgeon).filter(Surgeon.surgeon_id == adjustment.new_surgeon_id).first()
        if not surgeon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Surgeon with ID {adjustment.new_surgeon_id} not found"
            )
        changes.append(f"Surgeon changed from {original_surgeon_id} to {adjustment.new_surgeon_id}")

    if adjustment.new_start_time and adjustment.new_start_time != original_start_time:
        changes.append(f"Start time changed from {original_start_time} to {adjustment.new_start_time}")

    if adjustment.new_duration_minutes and adjustment.new_duration_minutes != original_duration:
        changes.append(f"Duration changed from {original_duration} to {adjustment.new_duration_minutes} minutes")

    # Check for conflicts if not forcing override
    conflicts = []
    if not adjustment.force_override and surgery.scheduled_date:
        # Apply temporary changes to check conflicts
        temp_surgery = Surgery(
            surgery_id=surgery.surgery_id,
            room_id=adjustment.new_room_id or surgery.room_id,
            surgeon_id=adjustment.new_surgeon_id or surgery.surgeon_id,
            start_time=adjustment.new_start_time or surgery.start_time,
            duration_minutes=adjustment.new_duration_minutes or surgery.duration_minutes,
            scheduled_date=surgery.scheduled_date
        )

        if temp_surgery.start_time and temp_surgery.duration_minutes:
            temp_surgery.end_time = temp_surgery.start_time + timedelta(minutes=temp_surgery.duration_minutes)

            # Check conflicts with this temporary surgery
            conflicts = _detect_schedule_conflicts(db, surgery.scheduled_date)
            # Filter conflicts that involve this surgery
            relevant_conflicts = [c for c in conflicts if c.surgery_id == surgery.surgery_id or c.conflicting_surgery_id == surgery.surgery_id]

            if relevant_conflicts:
                return {
                    "success": False,
                    "message": "Schedule adjustment would create conflicts",
                    "conflicts": [c.model_dump() for c in relevant_conflicts],
                    "changes_preview": changes
                }

    # Apply the changes
    if adjustment.new_room_id:
        surgery.room_id = adjustment.new_room_id
    if adjustment.new_surgeon_id:
        surgery.surgeon_id = adjustment.new_surgeon_id
    if adjustment.new_start_time:
        surgery.start_time = adjustment.new_start_time
    if adjustment.new_duration_minutes:
        surgery.duration_minutes = adjustment.new_duration_minutes
        if surgery.start_time:
            surgery.end_time = surgery.start_time + timedelta(minutes=surgery.duration_minutes)

    # Update surgery room assignment if it exists
    if surgery.room_id and surgery.start_time and surgery.end_time:
        assignment = db.query(SurgeryRoomAssignment).filter(
            SurgeryRoomAssignment.surgery_id == surgery.surgery_id
        ).first()

        if assignment:
            assignment.room_id = surgery.room_id
            assignment.start_time = surgery.start_time
            assignment.end_time = surgery.end_time
        else:
            # Create new assignment
            new_assignment = SurgeryRoomAssignment(
                surgery_id=surgery.surgery_id,
                room_id=surgery.room_id,
                start_time=surgery.start_time,
                end_time=surgery.end_time
            )
            db.add(new_assignment)

    db.commit()

    # Save to history
    if surgery.scheduled_date:
        current_schedule = await get_current_schedule(surgery.scheduled_date, db, current_user)
        _save_schedule_history(
            db=db,
            schedule_date=surgery.scheduled_date,
            user_id=current_user.user_id,
            action_type="manual_adjustment",
            changes_summary=f"Manual adjustment: {adjustment.reason}. Changes: {'; '.join(changes)}",
            affected_surgeries=[surgery.surgery_id],
            schedule_snapshot=current_schedule
        )

    return {
        "success": True,
        "message": f"Surgery {surgery.surgery_id} successfully adjusted",
        "changes_applied": changes,
        "conflicts_detected": len(conflicts),
        "forced_override": adjustment.force_override
    }


@router.post("/compare", response_model=ScheduleComparison)
async def compare_schedules(
    current_schedule: List[ScheduleAssignment],
    proposed_schedule: List[ScheduleAssignment],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Compare two schedules and analyze differences.

    Args:
        current_schedule: Current schedule assignments
        proposed_schedule: Proposed schedule assignments
        db: Database session
        current_user: Current authenticated user

    Returns:
        ScheduleComparison: Detailed comparison of the two schedules
    """
    changes = []

    # Create lookup dictionaries
    current_dict = {s.surgery_id: s for s in current_schedule}
    proposed_dict = {s.surgery_id: s for s in proposed_schedule}

    # Find changes
    all_surgery_ids = set(current_dict.keys()) | set(proposed_dict.keys())

    for surgery_id in all_surgery_ids:
        current_assignment = current_dict.get(surgery_id)
        proposed_assignment = proposed_dict.get(surgery_id)

        if not current_assignment:
            changes.append({
                "surgery_id": surgery_id,
                "change_type": "added",
                "description": f"Surgery {surgery_id} added to schedule"
            })
        elif not proposed_assignment:
            changes.append({
                "surgery_id": surgery_id,
                "change_type": "removed",
                "description": f"Surgery {surgery_id} removed from schedule"
            })
        else:
            # Check for differences
            surgery_changes = []

            if current_assignment.room_id != proposed_assignment.room_id:
                surgery_changes.append(f"room changed from {current_assignment.room} to {proposed_assignment.room}")

            if current_assignment.surgeon_id != proposed_assignment.surgeon_id:
                surgery_changes.append(f"surgeon changed from {current_assignment.surgeon} to {proposed_assignment.surgeon}")

            if current_assignment.start_time != proposed_assignment.start_time:
                surgery_changes.append(f"start time changed from {current_assignment.start_time} to {proposed_assignment.start_time}")

            if current_assignment.duration_minutes != proposed_assignment.duration_minutes:
                surgery_changes.append(f"duration changed from {current_assignment.duration_minutes} to {proposed_assignment.duration_minutes} minutes")

            if surgery_changes:
                changes.append({
                    "surgery_id": surgery_id,
                    "change_type": "modified",
                    "description": f"Surgery {surgery_id}: {'; '.join(surgery_changes)}"
                })

    # Calculate metrics comparison
    def calculate_schedule_metrics(schedule: List[ScheduleAssignment]) -> Dict[str, float]:
        if not schedule:
            return {"utilization": 0.0, "total_duration": 0.0, "surgeries_count": 0.0}

        total_duration = sum(s.duration_minutes for s in schedule)
        unique_rooms = len(set(s.room_id for s in schedule))

        return {
            "utilization": total_duration / (unique_rooms * 480) if unique_rooms > 0 else 0.0,  # Assuming 8-hour days
            "total_duration": float(total_duration),
            "surgeries_count": float(len(schedule)),
            "rooms_used": float(unique_rooms)
        }

    current_metrics = calculate_schedule_metrics(current_schedule)
    proposed_metrics = calculate_schedule_metrics(proposed_schedule)

    metrics_comparison = {
        "current": current_metrics,
        "proposed": proposed_metrics
    }

    # Generate improvement summary
    utilization_change = proposed_metrics["utilization"] - current_metrics["utilization"]
    surgery_count_change = proposed_metrics["surgeries_count"] - current_metrics["surgeries_count"]

    improvement_parts = []
    if utilization_change > 0.01:
        improvement_parts.append(f"utilization improved by {utilization_change:.1%}")
    elif utilization_change < -0.01:
        improvement_parts.append(f"utilization decreased by {abs(utilization_change):.1%}")

    if surgery_count_change > 0:
        improvement_parts.append(f"{int(surgery_count_change)} more surgeries scheduled")
    elif surgery_count_change < 0:
        improvement_parts.append(f"{int(abs(surgery_count_change))} fewer surgeries scheduled")

    improvement_summary = "; ".join(improvement_parts) if improvement_parts else "No significant changes in metrics"

    return ScheduleComparison(
        current_schedule=current_schedule,
        proposed_schedule=proposed_schedule,
        changes=changes,
        metrics_comparison=metrics_comparison,
        improvement_summary=improvement_summary
    )


@router.get("/history", response_model=List[ScheduleHistoryEntry])
async def get_schedule_history(
    schedule_date: Optional[date] = Query(None, description="Filter by schedule date"),
    limit: int = Query(50, ge=1, le=200, description="Number of history entries to return"),
    offset: int = Query(0, ge=0, description="Number of entries to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get schedule change history.

    Args:
        schedule_date: Optional date filter
        limit: Number of entries to return
        offset: Number of entries to skip
        db: Database session
        current_user: Current authenticated user

    Returns:
        List[ScheduleHistoryEntry]: Schedule history entries
    """
    query = (
        db.query(ScheduleHistory, User)
        .join(User, ScheduleHistory.created_by_user_id == User.user_id)
        .order_by(ScheduleHistory.created_at.desc())
    )

    if schedule_date:
        query = query.filter(ScheduleHistory.schedule_date == schedule_date)

    history_records = query.offset(offset).limit(limit).all()

    history_entries = []
    for history, user in history_records:
        # Parse JSON fields
        affected_surgeries = json.loads(history.affected_surgeries) if history.affected_surgeries else []
        schedule_snapshot = []
        if history.schedule_snapshot:
            try:
                snapshot_data = json.loads(history.schedule_snapshot)
                schedule_snapshot = [ScheduleAssignment(**item) for item in snapshot_data]
            except (json.JSONDecodeError, TypeError):
                schedule_snapshot = []

        history_entries.append(ScheduleHistoryEntry(
            history_id=history.history_id,
            schedule_date=history.schedule_date,
            created_at=history.created_at,
            created_by_user_id=history.created_by_user_id,
            created_by_username=user.username,
            action_type=history.action_type,
            changes_summary=history.changes_summary,
            affected_surgeries=affected_surgeries,
            schedule_snapshot=schedule_snapshot
        ))

    return history_entries
