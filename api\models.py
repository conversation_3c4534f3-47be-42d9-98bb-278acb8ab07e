"""
Pydantic models for the FastAPI application.

This module provides Pydantic models for request and response validation.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, EmailStr, ConfigDict, Field, field_validator


class SurgeryStatus(str, Enum):
    """Enum for surgery status."""
    SCHEDULED = "Scheduled"
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    CANCELLED = "Cancelled"


class AppointmentStatus(str, Enum):
    """Enum for appointment status."""
    SCHEDULED = "Scheduled"
    COMPLETED = "Completed"
    CANCELLED = "Cancelled"


class UrgencyLevel(str, Enum):
    """Enum for surgery urgency level."""
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    EMERGENCY = "Emergency"


# Base models (for shared attributes)
class PatientBase(BaseModel):
    """Base model for patient data."""
    name: str
    dob: date
    contact_info: Optional[str] = None
    privacy_consent: bool = False


class SurgeonBase(BaseModel):
    """Base model for surgeon data."""
    name: str
    contact_info: Optional[str] = None
    specialization: str
    credentials: str
    availability: bool = True


class StaffBase(BaseModel):
    """Base model for staff data."""
    name: str
    role: str
    contact_info: Optional[str] = None
    specialization: Optional[str] = None
    availability: bool = True


class OperatingRoomBase(BaseModel):
    """Base model for operating room data."""
    location: str


class SurgeryBase(BaseModel):
    """Base model for surgery data."""
    surgery_type_id: int
    duration_minutes: int
    urgency_level: UrgencyLevel = UrgencyLevel.MEDIUM
    patient_id: Optional[int] = None
    surgeon_id: Optional[int] = None
    room_id: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: SurgeryStatus = SurgeryStatus.SCHEDULED


class SurgeryTypeBase(BaseModel):
    """Base model for surgery type data."""
    name: str = Field(..., min_length=1, max_length=100, description="Surgery type name")
    description: Optional[str] = Field(None, max_length=500, description="Surgery type description")
    average_duration: int = Field(..., gt=0, le=1440, description="Average duration in minutes (1-1440)")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Surgery type name cannot be empty')
        return v.strip()


class SequenceDependentSetupTimeBase(BaseModel):
    """Base model for sequence-dependent setup time data."""
    from_surgery_type_id: int = Field(..., gt=0, description="Source surgery type ID")
    to_surgery_type_id: int = Field(..., gt=0, description="Target surgery type ID")
    setup_time_minutes: int = Field(..., ge=0, le=480, description="Setup time in minutes (0-480)")

    @field_validator('to_surgery_type_id')
    @classmethod
    def validate_different_surgery_types(cls, v, info):
        if 'from_surgery_type_id' in info.data and v == info.data['from_surgery_type_id']:
            raise ValueError('From and to surgery types must be different')
        return v


class AppointmentBase(BaseModel):
    """Base model for appointment data."""
    patient_id: int
    surgeon_id: int
    room_id: Optional[int] = None
    appointment_date: datetime
    status: AppointmentStatus = AppointmentStatus.SCHEDULED
    notes: Optional[str] = None


class UserBase(BaseModel):
    """Base model for user data."""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    role: str
    staff_id: Optional[int] = None


# Create models (for request payloads)
class PatientCreate(PatientBase):
    """Model for creating a patient."""
    pass


class SurgeonCreate(SurgeonBase):
    """Model for creating a surgeon."""
    pass


class StaffCreate(StaffBase):
    """Model for creating a staff member."""
    pass


class OperatingRoomCreate(OperatingRoomBase):
    """Model for creating an operating room."""
    pass


class SurgeryCreate(SurgeryBase):
    """Model for creating a surgery."""
    pass


class SurgeryTypeCreate(SurgeryTypeBase):
    """Model for creating a surgery type."""
    pass


class SequenceDependentSetupTimeCreate(SequenceDependentSetupTimeBase):
    """Model for creating a sequence-dependent setup time."""
    pass


class AppointmentCreate(AppointmentBase):
    """Model for creating an appointment."""
    pass


class UserCreate(UserBase):
    """Model for creating a user."""
    password: str


# Response models (for API responses)
class Patient(PatientBase):
    """Model for patient response."""
    patient_id: int

    model_config = ConfigDict(from_attributes=True)


class Surgeon(SurgeonBase):
    """Model for surgeon response."""
    surgeon_id: int

    model_config = ConfigDict(from_attributes=True)


class Staff(StaffBase):
    """Model for staff response."""
    staff_id: int

    model_config = ConfigDict(from_attributes=True)


class OperatingRoom(OperatingRoomBase):
    """Model for operating room response."""
    room_id: int

    model_config = ConfigDict(from_attributes=True)


class SurgeryType(SurgeryTypeBase):
    """Model for surgery type response."""
    type_id: int

    model_config = ConfigDict(from_attributes=True)


class SequenceDependentSetupTime(SequenceDependentSetupTimeBase):
    """Model for sequence-dependent setup time response."""
    id: int

    model_config = ConfigDict(from_attributes=True)


class Surgery(SurgeryBase):
    """Model for surgery response."""
    surgery_id: int

    model_config = ConfigDict(from_attributes=True)


class Appointment(AppointmentBase):
    """Model for appointment response."""
    appointment_id: int

    model_config = ConfigDict(from_attributes=True)


class User(UserBase):
    """Model for user response."""
    user_id: int
    is_active: bool
    created_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Authentication models
class Token(BaseModel):
    """Model for authentication token."""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Model for token data."""
    username: Optional[str] = None
    role: Optional[str] = None


# Update models (for partial updates)
class PatientUpdate(BaseModel):
    """Model for updating a patient."""
    name: Optional[str] = None
    contact_info: Optional[str] = None
    privacy_consent: Optional[bool] = None


class SurgeonUpdate(BaseModel):
    """Model for updating a surgeon."""
    name: Optional[str] = None
    contact_info: Optional[str] = None
    specialization: Optional[str] = None
    credentials: Optional[str] = None
    availability: Optional[bool] = None


class StaffUpdate(BaseModel):
    """Model for updating a staff member."""
    name: Optional[str] = None
    role: Optional[str] = None
    contact_info: Optional[str] = None
    specialization: Optional[str] = None
    availability: Optional[bool] = None


class OperatingRoomUpdate(BaseModel):
    """Model for updating an operating room."""
    location: Optional[str] = None


class SurgeryUpdate(BaseModel):
    """Model for updating a surgery."""
    surgery_type_id: Optional[int] = None
    duration_minutes: Optional[int] = None
    urgency_level: Optional[UrgencyLevel] = None
    patient_id: Optional[int] = None
    surgeon_id: Optional[int] = None
    room_id: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: Optional[SurgeryStatus] = None


class SurgeryTypeUpdate(BaseModel):
    """Model for updating a surgery type."""
    name: Optional[str] = None
    description: Optional[str] = None
    average_duration: Optional[int] = None


class SequenceDependentSetupTimeUpdate(BaseModel):
    """Model for updating a sequence-dependent setup time."""
    from_surgery_type_id: Optional[int] = None
    to_surgery_type_id: Optional[int] = None
    setup_time_minutes: Optional[int] = None


class UserUpdate(BaseModel):
    """Model for updating a user."""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[str] = None
    staff_id: Optional[int] = None
    is_active: Optional[bool] = None


# Bulk operation models
class BulkSDSTImport(BaseModel):
    """Model for bulk importing SDST data."""
    sdst_data: List[SequenceDependentSetupTimeCreate]
    overwrite_existing: bool = False


class BulkSDSTExport(BaseModel):
    """Model for bulk exporting SDST data."""
    surgery_type_ids: Optional[List[int]] = None  # If None, export all


class SDSTMatrix(BaseModel):
    """Model for SDST matrix representation."""
    surgery_types: List[SurgeryType]
    setup_times: List[SequenceDependentSetupTime]
    matrix: Dict[str, Dict[str, int]]  # from_type_id -> to_type_id -> setup_time


# Enhanced response models for frontend consumption
class SurgeryEnriched(BaseModel):
    """Enhanced surgery model with related entity names for frontend consumption."""
    surgery_id: int
    surgery_type_id: int
    surgery_type: str  # Surgery type name
    duration_minutes: int
    urgency_level: UrgencyLevel
    patient_id: Optional[int] = None
    patient_name: Optional[str] = None
    surgeon_id: Optional[int] = None
    surgeon_name: Optional[str] = None
    room_id: Optional[int] = None
    room_name: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: SurgeryStatus

    model_config = ConfigDict(from_attributes=True)


class ScheduleAssignment(BaseModel):
    """Model for schedule assignment with enriched data."""
    surgery_id: int
    room_id: int
    room: str  # e.g., "OR-1"
    surgeon_id: Optional[int] = None
    surgeon: Optional[str] = None  # e.g., "Dr. Smith"
    surgery_type_id: int
    surgery_type: str  # e.g., "Appendectomy"
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    patient_id: Optional[int] = None
    patient_name: Optional[str] = None
    urgency_level: Optional[UrgencyLevel] = None
    status: Optional[SurgeryStatus] = None


# Enhanced Optimization Models for Task 2.1
class OptimizationAlgorithm(str, Enum):
    """Enum for optimization algorithm types."""
    BASIC_TABU = "basic_tabu"
    ADAPTIVE_TABU = "adaptive_tabu"
    REACTIVE_TABU = "reactive_tabu"
    HYBRID_TABU = "hybrid_tabu"


class OptimizationStatus(str, Enum):
    """Enum for optimization status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AdvancedOptimizationParameters(BaseModel):
    """Enhanced model for advanced optimization parameters."""
    # Basic parameters
    schedule_date: Optional[date] = None
    max_iterations: int = Field(default=100, ge=10, le=10000, description="Maximum number of iterations")
    tabu_tenure: int = Field(default=10, ge=1, le=100, description="Base tabu tenure")
    max_no_improvement: int = Field(default=20, ge=5, le=1000, description="Max iterations without improvement")
    time_limit_seconds: int = Field(default=300, ge=10, le=3600, description="Time limit in seconds")

    # Algorithm selection
    algorithm: OptimizationAlgorithm = Field(default=OptimizationAlgorithm.BASIC_TABU, description="Algorithm variant to use")

    # Advanced tabu parameters
    min_tabu_tenure: Optional[int] = Field(default=None, ge=1, le=50, description="Minimum tabu tenure for adaptive algorithms")
    max_tabu_tenure: Optional[int] = Field(default=None, ge=10, le=200, description="Maximum tabu tenure for adaptive algorithms")
    tenure_adaptation_factor: float = Field(default=1.2, ge=1.0, le=3.0, description="Factor for tenure adaptation")

    # Diversification parameters
    diversification_threshold: int = Field(default=50, ge=10, le=500, description="Iterations before diversification")
    diversification_strength: float = Field(default=0.3, ge=0.1, le=1.0, description="Strength of diversification (0.1-1.0)")

    # Intensification parameters
    intensification_threshold: int = Field(default=25, ge=5, le=200, description="Iterations for intensification")
    intensification_factor: float = Field(default=0.8, ge=0.1, le=1.0, description="Factor for intensification")

    # Multi-objective weights
    weights: Optional[Dict[str, float]] = Field(default=None, description="Custom weights for evaluation criteria")

    # Performance parameters
    enable_progress_tracking: bool = Field(default=True, description="Enable real-time progress tracking")
    progress_update_interval: int = Field(default=10, ge=1, le=100, description="Progress update interval (iterations)")
    enable_detailed_logging: bool = Field(default=False, description="Enable detailed optimization logging")

    # Caching parameters
    cache_results: bool = Field(default=True, description="Cache optimization results")
    cache_key: Optional[str] = Field(default=None, description="Custom cache key")


class OptimizationProgress(BaseModel):
    """Model for optimization progress tracking."""
    optimization_id: str
    status: OptimizationStatus
    current_iteration: int
    total_iterations: int
    best_score: float
    current_score: float
    iterations_without_improvement: int
    elapsed_time_seconds: float
    estimated_remaining_seconds: Optional[float] = None
    progress_percentage: float
    algorithm_used: OptimizationAlgorithm
    last_update: datetime


class OptimizationResult(BaseModel):
    """Enhanced model for optimization result response."""
    optimization_id: str
    assignments: List[ScheduleAssignment]
    score: float
    detailed_metrics: Dict[str, float]
    iteration_count: int
    execution_time_seconds: float
    algorithm_used: OptimizationAlgorithm
    parameters_used: AdvancedOptimizationParameters
    convergence_data: List[Dict[str, Any]] = Field(default_factory=list, description="Score progression data")
    solution_quality_analysis: Dict[str, Any] = Field(default_factory=dict, description="Quality analysis")
    cached: bool = Field(default=False, description="Whether result was retrieved from cache")


class OptimizationComparison(BaseModel):
    """Model for comparing optimization results."""
    baseline_result: OptimizationResult
    comparison_results: List[OptimizationResult]
    performance_comparison: Dict[str, Dict[str, float]]
    recommendation: str
    best_algorithm: OptimizationAlgorithm
    improvement_summary: str


class OptimizationMetrics(BaseModel):
    """Model for optimization metrics."""
    or_utilization: float
    setup_times: float
    surgeon_preferences: float
    workload_balance: float
    patient_wait_time: float
    emergency_priority: float
    operational_costs: float
    staff_overtime: float


class OptimizationResultEnriched(BaseModel):
    """Enhanced optimization result model for frontend consumption."""
    assignments: List[ScheduleAssignment]
    score: float
    metrics: OptimizationMetrics
    iteration_count: int
    execution_time_seconds: float
    total_surgeries: int
    rooms_utilized: int
    average_utilization: float


class ErrorResponse(BaseModel):
    """Standardized error response model."""
    detail: str
    error_code: Optional[str] = None
    field_errors: Optional[Dict[str, List[str]]] = None


# Enhanced Schedule Management Models
class ScheduleConflict(BaseModel):
    """Model for schedule conflict information."""
    conflict_type: str = Field(..., description="Type of conflict (time_overlap, resource_conflict, etc.)")
    surgery_id: int = Field(..., description="ID of the conflicting surgery")
    conflicting_surgery_id: Optional[int] = Field(None, description="ID of the surgery it conflicts with")
    resource_type: Optional[str] = Field(None, description="Type of resource in conflict (room, surgeon, equipment)")
    resource_id: Optional[int] = Field(None, description="ID of the conflicting resource")
    conflict_start: datetime = Field(..., description="Start time of the conflict")
    conflict_end: datetime = Field(..., description="End time of the conflict")
    severity: str = Field(..., description="Conflict severity (critical, warning, info)")
    message: str = Field(..., description="Human-readable conflict description")


class ScheduleValidationResult(BaseModel):
    """Model for schedule validation results."""
    is_valid: bool = Field(..., description="Whether the schedule is valid")
    conflicts: List[ScheduleConflict] = Field(default_factory=list, description="List of conflicts found")
    warnings: List[str] = Field(default_factory=list, description="List of warnings")
    total_conflicts: int = Field(..., description="Total number of conflicts")
    critical_conflicts: int = Field(..., description="Number of critical conflicts")


class ManualScheduleAdjustment(BaseModel):
    """Model for manual schedule adjustments."""
    surgery_id: int = Field(..., description="ID of the surgery to adjust")
    new_room_id: Optional[int] = Field(None, description="New room assignment")
    new_surgeon_id: Optional[int] = Field(None, description="New surgeon assignment")
    new_start_time: Optional[datetime] = Field(None, description="New start time")
    new_duration_minutes: Optional[int] = Field(None, gt=0, le=1440, description="New duration in minutes")
    reason: str = Field(..., min_length=1, max_length=500, description="Reason for the adjustment")
    force_override: bool = Field(False, description="Force adjustment even if conflicts exist")


class ScheduleComparison(BaseModel):
    """Model for comparing two schedules."""
    current_schedule: List[ScheduleAssignment]
    proposed_schedule: List[ScheduleAssignment]
    changes: List[Dict[str, Any]] = Field(default_factory=list, description="List of changes between schedules")
    metrics_comparison: Dict[str, Dict[str, float]] = Field(default_factory=dict, description="Metrics comparison")
    improvement_summary: str = Field(..., description="Summary of improvements or changes")


class ScheduleHistoryEntry(BaseModel):
    """Model for schedule history tracking."""
    history_id: int
    schedule_date: date
    created_at: datetime
    created_by_user_id: int
    created_by_username: str
    action_type: str = Field(..., description="Type of action (manual_adjustment, optimization, bulk_update)")
    changes_summary: str = Field(..., description="Summary of changes made")
    affected_surgeries: List[int] = Field(default_factory=list, description="List of affected surgery IDs")
    schedule_snapshot: List[ScheduleAssignment] = Field(default_factory=list, description="Snapshot of the schedule")

    model_config = ConfigDict(from_attributes=True)


# Additional Enhanced Optimization Models
class OptimizationCache(BaseModel):
    """Model for optimization result caching."""
    cache_key: str
    parameters_hash: str
    result: OptimizationResult
    created_at: datetime
    expires_at: datetime
    hit_count: int = 0


class AlgorithmPerformanceMetrics(BaseModel):
    """Model for algorithm performance tracking."""
    algorithm: OptimizationAlgorithm
    average_score: float
    average_execution_time: float
    success_rate: float
    total_runs: int
    best_score_achieved: float
    convergence_rate: float


class OptimizationSession(BaseModel):
    """Model for optimization session tracking."""
    session_id: str
    user_id: int
    created_at: datetime
    parameters: AdvancedOptimizationParameters
    status: OptimizationStatus
    progress: Optional[OptimizationProgress] = None
    result: Optional[OptimizationResult] = None
    error_message: Optional[str] = None


class OptimizationAnalysis(BaseModel):
    """Model for detailed optimization analysis."""
    optimization_id: str
    solution_quality_score: float
    constraint_violations: List[Dict[str, Any]] = Field(default_factory=list)
    resource_utilization: Dict[str, float] = Field(default_factory=dict)
    bottleneck_analysis: Dict[str, Any] = Field(default_factory=dict)
    improvement_suggestions: List[str] = Field(default_factory=list)
    sensitivity_analysis: Dict[str, float] = Field(default_factory=dict)


class OptimizationBenchmark(BaseModel):
    """Model for optimization benchmarking."""
    benchmark_id: str
    test_case_name: str
    algorithms_tested: List[OptimizationAlgorithm]
    results: List[OptimizationResult]
    performance_metrics: Dict[str, AlgorithmPerformanceMetrics]
    winner: OptimizationAlgorithm
    execution_summary: str
