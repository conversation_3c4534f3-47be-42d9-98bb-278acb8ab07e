<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surgery Scheduler - Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/primevue@3.26.1/resources/themes/saga-blue/theme.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/primevue@3.26.1/resources/primevue.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/primeicons@6.0.1/primeicons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/primeflex@3.3.0/primeflex.min.css">
    <style>
        body {
            font-family: var(--font-family);
            margin: 0;
            padding: 0;
            background-color: var(--surface-ground);
            color: var(--text-color);
        }
        .layout-wrapper {
            display: flex;
            min-height: 100vh;
        }
        .layout-sidebar {
            width: 250px;
            background-color: var(--surface-card);
            box-shadow: 0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12);
            z-index: 999;
            overflow-y: auto;
        }
        .layout-sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--surface-border);
            text-align: center;
        }
        .layout-sidebar-header h2 {
            margin: 0;
            color: var(--primary-color);
        }
        .layout-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .layout-menu-item {
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
        }
        .layout-menu-item:hover {
            background-color: var(--surface-hover);
        }
        .layout-menu-item.active {
            background-color: var(--primary-50);
            color: var(--primary-color);
            font-weight: 600;
        }
        .layout-menu-item i {
            margin-right: 0.75rem;
        }
        .layout-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }
        .layout-topbar {
            background-color: var(--surface-card);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px -1px rgba(0,0,0,.1);
        }
        .layout-topbar-left h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .layout-topbar-right {
            display: flex;
            align-items: center;
        }
        .user-profile {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .user-profile-name {
            margin-right: 0.5rem;
        }
        .card {
            background-color: var(--surface-card);
            border-radius: 10px;
            box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .card-header i {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-right: 0.75rem;
        }
        .card-header h2 {
            margin: 0;
            font-size: 1.25rem;
        }
        .grid {
            display: flex;
            flex-wrap: wrap;
            margin-right: -0.5rem;
            margin-left: -0.5rem;
            margin-top: -0.5rem;
        }
        .col {
            flex: 1 1 0;
            padding: 0.5rem;
        }
        .col-12 {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0.5rem;
        }
        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0.5rem;
        }
        .col-4 {
            flex: 0 0 33.3333%;
            max-width: 33.3333%;
            padding: 0.5rem;
        }
        .stat-card {
            background-color: var(--surface-card);
            border-radius: 10px;
            box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
            padding: 1.5rem;
            display: flex;
            align-items: center;
        }
        .stat-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 1rem;
        }
        .stat-card-icon.blue {
            background-color: var(--blue-100);
            color: var(--blue-500);
        }
        .stat-card-icon.green {
            background-color: var(--green-100);
            color: var(--green-500);
        }
        .stat-card-icon.orange {
            background-color: var(--orange-100);
            color: var(--orange-500);
        }
        .stat-card-icon.purple {
            background-color: var(--purple-100);
            color: var(--purple-500);
        }
        .stat-card-icon i {
            font-size: 1.5rem;
        }
        .stat-card-content {
            flex: 1;
        }
        .stat-card-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }
        .stat-card-label {
            color: var(--text-color-secondary);
            margin: 0;
        }
        .logout-button {
            background-color: var(--red-500);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            margin-left: 1rem;
        }
        .logout-button:hover {
            background-color: var(--red-600);
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .layout-wrapper {
                flex-direction: column;
            }
            .layout-sidebar {
                width: 100%;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            .layout-sidebar.active {
                transform: translateX(0);
            }
            .layout-content {
                margin-top: 60px;
            }
            .layout-topbar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 998;
            }
            .menu-button {
                display: block;
                margin-right: 1rem;
            }
            .col-6, .col-4 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <div id="sidebar" class="layout-sidebar">
            <div class="layout-sidebar-header">
                <h2>Surgery Scheduler</h2>
            </div>
            <ul class="layout-menu">
                <li class="layout-menu-item active">
                    <i class="pi pi-home"></i>
                    <span>Dashboard</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-calendar"></i>
                    <span>Schedule</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-user-plus"></i>
                    <span>Surgeons</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-users"></i>
                    <span>Patients</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-building"></i>
                    <span>Operating Rooms</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-chart-bar"></i>
                    <span>Reports</span>
                </li>
                <li class="layout-menu-item">
                    <i class="pi pi-cog"></i>
                    <span>Settings</span>
                </li>
            </ul>
        </div>
        
        <div class="layout-content">
            <div class="layout-topbar">
                <div class="layout-topbar-left">
                    <button id="menuButton" class="menu-button" style="display: none;">
                        <i class="pi pi-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                <div class="layout-topbar-right">
                    <div id="userProfile" class="user-profile">
                        <span id="userName" class="user-profile-name">Loading...</span>
                        <i class="pi pi-user"></i>
                    </div>
                    <button id="logoutButton" class="logout-button">
                        <i class="pi pi-sign-out"></i> Logout
                    </button>
                </div>
            </div>
            
            <div class="grid">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="pi pi-info-circle"></i>
                            <h2>Welcome to Surgery Scheduler</h2>
                        </div>
                        <p>You have successfully logged in to the Surgery Scheduler application. This is a simplified dashboard for demonstration purposes.</p>
                    </div>
                </div>
                
                <div class="col-3">
                    <div class="stat-card">
                        <div class="stat-card-icon blue">
                            <i class="pi pi-calendar"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-value">12</h3>
                            <p class="stat-card-label">Today's Surgeries</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-3">
                    <div class="stat-card">
                        <div class="stat-card-icon green">
                            <i class="pi pi-user-plus"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-value">8</h3>
                            <p class="stat-card-label">Available Surgeons</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-3">
                    <div class="stat-card">
                        <div class="stat-card-icon orange">
                            <i class="pi pi-building"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-value">5</h3>
                            <p class="stat-card-label">Operating Rooms</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-3">
                    <div class="stat-card">
                        <div class="stat-card-icon purple">
                            <i class="pi pi-users"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-value">24</h3>
                            <p class="stat-card-label">Patients</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = 'app_login.html';
                return;
            }
            
            // Get user data
            const userData = JSON.parse(localStorage.getItem('user') || '{}');
            const userName = document.getElementById('userName');
            if (userData.full_name) {
                userName.textContent = userData.full_name;
            } else if (userData.username) {
                userName.textContent = userData.username;
            }
            
            // Logout button
            const logoutButton = document.getElementById('logoutButton');
            logoutButton.addEventListener('click', function() {
                localStorage.removeItem('token');
                localStorage.removeItem('token_type');
                localStorage.removeItem('user');
                window.location.href = 'app_login.html';
            });
            
            // Mobile menu toggle
            const menuButton = document.getElementById('menuButton');
            const sidebar = document.getElementById('sidebar');
            
            function checkScreenSize() {
                if (window.innerWidth <= 768) {
                    menuButton.style.display = 'block';
                } else {
                    menuButton.style.display = 'none';
                    sidebar.classList.remove('active');
                }
            }
            
            menuButton.addEventListener('click', function() {
                sidebar.classList.toggle('active');
            });
            
            window.addEventListener('resize', checkScreenSize);
            checkScreenSize();
        });
    </script>
</body>
</html>
