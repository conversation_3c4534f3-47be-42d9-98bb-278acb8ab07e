Generic single-database configuration with Alembic.

This directory contains Alembic migration scripts for the surgery scheduling application.

To create a new migration:
```
alembic revision --autogenerate -m "description of changes"
```

To upgrade the database to the latest revision:
```
alembic upgrade head
```

To downgrade the database to a specific revision:
```
alembic downgrade <revision>
```

For more information on Alembic, see:
https://alembic.sqlalchemy.org/en/latest/
