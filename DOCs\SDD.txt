Software Design Document

Project: Surgery Scheduling System App
Version: 1.0
Date: May 16, 2025
Author/Student: [Your Name/ID Here]

Table of Contents:
(To be populated as we proceed)

1. Introduction

1.1 Purpose
This Software Design Document (SDD) describes the design of the Surgery Scheduling System App. It details the system architecture, module design, data design, user interface design, and other technical aspects necessary for the implementation of the system. The purpose of this SDD is to provide a technical blueprint for developers (in this case, the student author) and to serve as a reference for understanding the system's internal structure and behavior. This design is intended to fulfill the requirements specified in the Software Requirements Document (SRD) for this project (Surgery Scheduling System App SRD, Version 1.0).

1.2 Scope
The scope of this SDD covers the design of all functionalities and components of the Surgery Scheduling System App as defined in the SRD, Version 1.0, Section 1.4 (Project Scope). This includes the design for managing elective and emergency surgery scheduling, resource allocation, user interactions, reporting, notifications, and integration with external systems like EHRs. The design specifically addresses the on-premises deployment constraint and the chosen technology stack.

1.3 Definitions, Acronyms, and Abbreviations

API: Application Programming Interface
CRUD: Create, Read, Update, Delete
DB: Database
EHR: Electronic Health Record
EMR: Electronic Medical Record
FR: Functional Requirement (from SRD)
GDPR: General Data Protection Regulation
HIPAA: Health Insurance Portability and Accountability Act
JSON: JavaScript Object Notation
JWT: JSON Web Token
NFR: Non-Functional Requirement (from SRD)
OR: Operating Room
PHI: Protected Health Information
REST: Representational State Transfer
SDD: Software Design Document
SRD: Software Requirements Document
UI: User Interface
UX: User Experience
CNDP: Commission Nationale de contrôle de la protection des Données à caractère Personnel (Morocco)
1.4 References

[SRD-01] Surgery Scheduling System App - Software Requirements Document, Version 1.0, May 16, 2025.
[THESIS-01] Bouguerra, Afef. "Optimisation et aide à la décision pour la programmation des opérations électives et urgentes." (For optimization concepts).
[TECH-01] React Documentation (react.dev)
[TECH-02] Node.js Documentation (nodejs.org)
[TECH-03] Express.js Documentation (expressjs.com)
[TECH-04] PostgreSQL Documentation (postgresql.org)
[DESIGN-STD-01] (Any specific design standards or patterns being followed, e.g., common architectural patterns).
1.5 Document Overview
This document is organized as follows:

Section 1: Provides an introduction, scope, definitions, references, and overview.
Section 2: Gives a system overview, including design goals and a high-level architecture.
Section 3: Details the architectural design, including styles, system structure, and deployment.
Section 4: Describes the detailed design of individual software modules.
Section 5: Outlines the data design, including the database schema and data management strategies.
Section 6: Specifies the user interface design principles and key screen mockups.
Section 7: Details the design of external interfaces, particularly for EHR integration.
Section 8: Describes the security design considerations.
Section 9: Provides a high-level overview of the core algorithm design.
Section 10: Discusses deployment and configuration.
Section 11: Outlines quality assurance provisions.
2. System Overview

2.1 Recap of System Purpose
As stated in SRD-01 [Section 1.1, 2.2], the Surgery Scheduling System App is designed to efficiently manage and optimize the scheduling of elective and emergency surgical operations. Key objectives include improving OR utilization, reducing patient wait times, optimizing resource allocation, and providing a robust decision-support tool for healthcare professionals. This system is a practical implementation for a final thesis project.

2.2 Design Goals
The design of the Surgery Scheduling System App aims to achieve the following primary goals, derived from the Non-Functional Requirements in SRD-01:

Modularity (NFR-MAINT-xxx): The system will be composed of loosely coupled, independently developable, and maintainable modules.
Scalability (NFR-SCALE-xxx): The architecture and database design will support future growth in data volume and user load, within the context of an on-premises deployment.
Security (NFR-SEC-xxx): The system will protect sensitive patient and operational data through robust authentication, authorization, data encryption, and adherence to privacy principles (HIPAA, GDPR-like, CNDP awareness).
Usability (NFR-USE-xxx): The user interface will be intuitive, efficient, and tailored to the needs of different user roles (Schedulers, Surgeons, Nurses, Admins).
Reliability (NFR-REL-xxx): The system will be designed for stable operation and include mechanisms for error handling and data integrity.
Maintainability (NFR-MAINT-xxx): The codebase will be well-structured, documented, and easy to modify or extend.
Performance (NFR-PERF-xxx): The system will provide acceptable response times for user interactions and efficient execution of scheduling optimization algorithms for representative datasets.
Integrability (SRD-SCOPE-006): The system will be designed to interface with external systems, particularly EHRs, via well-defined APIs.
2.3 High-Level Architecture
The Surgery Scheduling System App will employ a Client-Server architectural model.

Client-Side (Frontend):

A web application built using React.
Responsible for rendering the User Interface (UI) in standard web browsers.
Handles user interactions and sends requests to the server via HTTP/S.
Receives data from the server and updates the UI dynamically.
Server-Side (Backend):

An application built using Node.js with the Express.js framework.
Hosts the business logic, scheduling algorithms, and API endpoints.
Manages communication with the database.
Handles authentication, authorization, and session management.
Provides RESTful APIs for client-server communication and external system integration.
Database (Persistence Layer):

A PostgreSQL relational database.
Stores all persistent data, including user accounts, patient information (references or minimal necessary data), surgery details, resource schedules, and audit logs.
External Interfaces:

A RESTful API layer provided by the backend will facilitate integration with external systems, primarily Electronic Health Record (EHR) systems, for data exchange.
Deployment Environment:

The entire system (backend, database) will be deployed on-premises within the hospital's IT infrastructure, as specified in SRD-01 [OE-001].
High-Level Architecture Diagram:

Code snippet

graph TD
    subgraph User Devices
        Client_Desktop[Web Browser - Desktop]
        Client_Tablet[Web Browser - Tablet]
    end

    subgraph On-Premises Hospital Network
        subgraph Application Server
            LB(Load Balancer - Optional for Scalability) --> BE_App[Backend: Node.js / Express.js API]
        end
        subgraph Database Server
            BE_App --> DB[Database: PostgreSQL]
        end
        subgraph Integration Point
            BE_App <-.->|RESTful API (HTTPS)| EHR[External EHR System]
        end
    end

    Client_Desktop -- HTTPS --> LB
    Client_Tablet -- HTTPS --> LB

    classDef client fill:#lightblue,stroke:#333,stroke-width:2px;
    classDef server fill:#lightgreen,stroke:#333,stroke-width:2px;
    classDef db fill:#orange,stroke:#333,stroke-width:2px;
    classDef external fill:#lightgrey,stroke:#333,stroke-width:2px;

    class Client_Desktop,Client_Tablet client;
    class BE_App server;
    class DB db;
    class EHR external;
(Note: The Mermaid diagram above is a textual representation. For the actual SDD, this would be a proper graphical diagram. "LB (Load Balancer)" is shown as optional, likely out of scope for an initial thesis project but good for showing scalability consideration).

Brief Description of Diagram:
Users access the system via web browsers on desktops or tablets. These clients communicate over HTTPS with the backend application server (potentially through a load balancer for larger setups, though initially direct). The backend, built with Node.js/Express.js, processes requests, applies business logic (including scheduling algorithms), interacts with the PostgreSQL database for data persistence, and exposes RESTful APIs. One key external interface is with the EHR system, also via a RESTful API. All server components are hosted on-premises.